package com.gumtree.web.cookie.cutters.threatmetrix;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.Cookie;

import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ThreatMetrixCookieCutterTest {

    @Mock
    private Cookie existingCookie;

    @Test
    public void cutExistingDodgyValue() {
        ThreatMetrixCookieCutter threatMetrixCookieCutter = new ThreatMetrixCookieCutter("domain");
        String existingValue = "3334";
        when(existingCookie.getValue()).thenReturn(existingValue);
        ThreatMetrixCookie newCookie = threatMetrixCookieCutter.cutExisting(existingCookie);
        assertNotEquals(newCookie.getDefaultValue(), existingValue);
    }

    @Test
    public void cutExistingUUIDValue() {

        String existingValue = UUID.randomUUID().toString();
        when(existingCookie.getValue()).thenReturn(existingValue);
        when(existingCookie.getMaxAge()).thenReturn(-1);

        ThreatMetrixCookieCutter threatMetrixCookieCutter = new ThreatMetrixCookieCutter("domain");
        ThreatMetrixCookie newCookie = threatMetrixCookieCutter.cutExisting(existingCookie);
        assertEquals(newCookie.getDefaultValue(), existingValue);
    }

    @Test
    public void cutExistingWhenMaxAgeIsZero() {

        String existingValue = UUID.randomUUID().toString();
        when(existingCookie.getValue()).thenReturn(existingValue);
        when(existingCookie.getMaxAge()).thenReturn(0);

        ThreatMetrixCookieCutter threatMetrixCookieCutter = new ThreatMetrixCookieCutter("domain");
        ThreatMetrixCookie newCookie = threatMetrixCookieCutter.cutExisting(existingCookie);
        assertNotEquals(newCookie.getDefaultValue(), existingValue);
    }

}